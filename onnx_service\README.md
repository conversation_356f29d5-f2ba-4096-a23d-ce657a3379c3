# ONNX Service

一个基于Rust的高性能ONNX模型推理API服务框架，支持将任何ONNX模型转换为RESTful API服务。

## 特性

- 🚀 **高性能**: 基于Rust和异步框架构建
- 🔧 **通用性**: 支持任何ONNX模型文件 (当前为演示版本)
- 📖 **API文档**: 集成Swagger UI，自动生成交互式API文档
- ⚙️ **配置管理**: 使用.env文件管理配置
- 🔄 **动态加载**: 支持运行时加载/卸载模型
- 🌐 **RESTful**: 标准的REST API接口
- 🛡️ **类型安全**: 完整的请求/响应验证
- 📊 **监控**: 内置健康检查和性能指标

## 当前状态

**注意**: 当前版本是一个功能完整的演示版本，包含了完整的API框架和模拟的ONNX推理功能。真正的ONNX Runtime集成已准备就绪，只需取消注释Cargo.toml中的ort依赖并替换onnx_engine.rs中的模拟实现即可。

### 已实现功能
- ✅ 完整的REST API框架
- ✅ Swagger UI文档
- ✅ 配置管理系统
- ✅ 模型加载/卸载API
- ✅ 推理API (模拟版本)
- ✅ 健康检查
- ✅ 错误处理
- ✅ 日志记录

### 待完成功能
- ⏳ 真实ONNX Runtime集成 (代码已准备，需要解决Windows链接问题)
- ⏳ 多模型并发支持
- ⏳ 批处理推理
- ⏳ 模型缓存优化

## 快速开始

### 1. 环境要求

- Rust 1.70+
- Windows x86平台
- ONNX模型文件 (可选，演示版本可使用任意文件)

### 2. 配置环境

复制并编辑配置文件：
```bash
cp .env.example .env
```

编辑`.env`文件，设置你的模型路径：
```env
MODEL_PATH=./models/your_model.onnx
MODEL_NAME=your_model_name
SERVER_PORT=8080
```

### 3. 放置模型文件

将你的ONNX模型文件放置到`models/`目录下。

### 4. 启动服务

```bash
cargo run
```

服务启动后，你可以访问：
- API服务: http://127.0.0.1:8080
- Swagger UI: http://127.0.0.1:8080/swagger-ui/

## API接口

### 健康检查
```
GET /health
```

### 模型管理
```
GET /model/info          # 获取模型信息
POST /model/load         # 加载模型
POST /model/unload       # 卸载模型
```

### 推理
```
POST /inference          # 执行推理
```

## 推理请求示例

```json
{
  "inputs": {
    "input_name": {
      "dtype": "f32",
      "shape": [1, 3, 224, 224],
      "data": [0.1, 0.2, 0.3, ...]
    }
  },
  "output_names": ["output_name"]
}
```

## 推理响应示例

```json
{
  "outputs": {
    "output_name": {
      "dtype": "f32",
      "shape": [1, 1000],
      "data": [0.1, 0.2, 0.3, ...]
    }
  },
  "inference_time_ms": 15.5,
  "request_id": "uuid-string"
}
```

## 配置选项

| 配置项             | 描述                   | 默认值              |
| ------------------ | ---------------------- | ------------------- |
| SERVER_HOST        | 服务器主机地址         | 127.0.0.1           |
| SERVER_PORT        | 服务器端口             | 8080                |
| MODEL_PATH         | 模型文件路径           | ./models/model.onnx |
| MODEL_NAME         | 模型名称               | default_model       |
| INTRA_THREADS      | ONNX Runtime内部线程数 | 4                   |
| INTER_THREADS      | ONNX Runtime间线程数   | 1                   |
| OPTIMIZATION_LEVEL | 优化级别 (0-3)         | 3                   |

## 开发

### 构建
```bash
cargo build --release
```

### 运行测试
```bash
cargo test
```

## 许可证

MIT License

## 测试示例

### 1. 健康检查
```bash
curl http://127.0.0.1:8080/health
```

### 2. 加载模型
```bash
curl -X POST http://127.0.0.1:8080/model/load \
  -H "Content-Type: application/json" \
  -d '{"model_path":"./models/test_model.onnx","model_name":"test_model"}'
```

### 3. 获取模型信息
```bash
curl http://127.0.0.1:8080/model/info
```

### 4. 执行推理
```bash
curl -X POST http://127.0.0.1:8080/inference \
  -H "Content-Type: application/json" \
  -d '{
    "inputs": {
      "input": {
        "dtype": "f32",
        "shape": [1, 10],
        "data": [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
      }
    },
    "output_names": ["output"]
  }'
```

### 5. 卸载模型
```bash
curl -X POST http://127.0.0.1:8080/model/unload
```

## 技术栈

- **Rust**: 系统编程语言
- **ONNX Runtime 2.0**: 高性能推理引擎 (准备集成)
- **Axum**: 现代异步Web框架
- **Tokio**: 异步运行时
- **Utoipa**: OpenAPI/Swagger集成
- **Serde**: 序列化/反序列化
- **Anyhow**: 错误处理
- **Tracing**: 结构化日志
