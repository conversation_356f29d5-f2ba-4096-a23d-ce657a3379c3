mod config;
mod handlers;
mod models;
mod onnx_engine;
mod swagger;

use anyhow::Result;
use axum::{
    routing::{get, post},
    Router,
};
use std::sync::Arc;
use tokio::sync::RwLock;
use tower::ServiceBuilder;
use tower_http::cors::CorsLayer;
use tracing::{error, info};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};
use utoipa::OpenApi;
use utoipa_swagger_ui::SwaggerUi;

use config::Config;
use handlers::{get_model_info, health_check, inference, load_model, unload_model, AppState};
use onnx_engine::OnnxEngine;
use swagger::ApiDoc;

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "onnx_service=info,tower_http=debug".into()),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();

    // 加载配置
    let config = Config::from_env()?;
    info!("Configuration loaded successfully");
    info!("Server will start on: {}", config.server_address());

    // 创建ONNX引擎
    let engine = Arc::new(RwLock::new(OnnxEngine::new(config.onnx.clone())));

    // 如果配置了模型路径，尝试自动加载模型
    if std::path::Path::new(&config.model.path).exists() {
        info!("Auto-loading model from: {}", config.model.path);
        let mut engine_guard = engine.write().await;
        match engine_guard
            .load_model(&config.model.path, Some(config.model.name.clone()))
            .await
        {
            Ok(model_info) => {
                info!("Model auto-loaded successfully: {}", model_info.name);
            }
            Err(e) => {
                error!("Failed to auto-load model: {}", e);
                info!("Server will start without a pre-loaded model");
            }
        }
    } else {
        info!(
            "Model file not found at configured path: {}",
            config.model.path
        );
        info!("Server will start without a pre-loaded model");
    }

    // 创建应用状态
    let app_state = AppState { engine };

    // 创建路由
    let app = create_router(app_state);

    // 启动服务器
    let listener = tokio::net::TcpListener::bind(&config.server_address()).await?;
    info!("🚀 ONNX Service started on {}", config.server_address());
    info!(
        "📖 Swagger UI available at: http://{}/swagger-ui/",
        config.server_address()
    );

    axum::serve(listener, app).await?;

    Ok(())
}

fn create_router(app_state: AppState) -> Router {
    // 模型管理路由
    let model_routes = Router::new()
        .route("/info", get(get_model_info))
        .route("/load", post(load_model))
        .route("/unload", post(unload_model));

    // 主路由
    Router::new()
        .merge(SwaggerUi::new("/swagger-ui").url("/api-docs/openapi.json", ApiDoc::openapi()))
        .route("/health", get(health_check))
        .route("/inference", post(inference))
        .nest("/model", model_routes)
        .with_state(app_state)
        .layer(
            ServiceBuilder::new()
                .layer(CorsLayer::permissive())
                .into_inner(),
        )
}
